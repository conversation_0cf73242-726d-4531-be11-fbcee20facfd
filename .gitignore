/node_modules
package-lock.json
public
.vscode
.svelte-kit
.github
.firebase
.env
.env.local
firestore.indexes.json
firestore.rules
firebase-debug.log
TODO.txt
vite.config.ts.timestamp-1706963615252-9ae37e49a4b32.mjs
vite.config.ts.timestamp-1722872206883-166aef4600ef5.mjs
node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store

# Task files
# tasks.json
# tasks/ 
.taskmaster/tasks/tasks.json
.augment
.windsurf
.claude

# Test files
*test