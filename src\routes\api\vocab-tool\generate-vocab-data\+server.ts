import { json } from "@sveltejs/kit";
import { Type, type GenerateContentConfig } from "@google/genai";
import { supabase } from "$lib/server/supabase";
import { geminiAI } from "$lib/server";
import { redis } from "$lib/server";
import { Ratelimit } from "@upstash/ratelimit";
import type { VocabCard } from "$lib/types/vocab.types";

// API endpoint for generating vocabulary card data using AI
export const POST = async ({ request }) => {
    const { uid, wordList, isQuizletImport } = await request.json();

    // Set up rate limiting: 60 requests per hour per user
    const rateLimit = new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(60, "1 h"),
    })

    // Check if user has exceeded rate limit
    const { success, remaining } = await rateLimit.limit(uid);

    // Reject request if rate limit exceeded or word list exceeds remaining quota
    if (!success || wordList.length > remaining) {
        return json({ message: "Exceeded rate limit. Please try again in an hour.", success: false });
    }

    try {
        // Process all words in parallel, generating vocab card data for each
        const vocabCards = await Promise.all(wordList.map((wordData: { word: string, definition: string }) => getSingleWordData(uid, wordData.word, wordData.definition, isQuizletImport)));
        return json({ success: true, vocabCards });
    } catch (error) {
        console.error('Error fetching vocab data:', error);
        return json({ success: false, message: 'An error occurred while fetching data. Please try again.' });
    }
}


// Generates vocabulary card data for a single word using AI
async function getSingleWordData(uid: string, word: string, definition: string, isQuizletImport: boolean) {
    // Apply rate limiting per user
    const rateLimit = new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(60, "1 h"),
    })
    const { success } = await rateLimit.limit(uid);
    if (!success) {
        throw new Error('Rate limit exceeded');
    }

    if (!isQuizletImport) {
        // Check if card already exists in database to avoid duplicate generation
        const { data: existingCard, error: queryError } = await supabase
            .from('vocabCards')
            .select('word, partOfSpeech, definition, charge, difficulty, hint1, hint2')
            .eq('word', word.trim())
            .eq('definition', definition.trim())
            .maybeSingle()

        if (existingCard) {
            return existingCard;
        }
    }

    // Generate vocab data using AI
    const data = await generateVocabData(word, definition);

    // Parse the AI-generated JSON response into VocabCard object
    const vocabData: VocabCard = JSON.parse(data);

    if (!isQuizletImport) {
        // Insert the new card into Supabase database for future reference
        const { data: insertedData, error: insertError } = await supabase
            .from('vocabCards')
            .insert(vocabData)
            .single();

        if (insertError) {
            console.error('Error inserting vocab data:', insertError);
            throw insertError;
        }
    }
    
    return vocabData;
}


// Configuration for Google's Gemini AI model to generate structured vocab card data
const config: GenerateContentConfig = {
    temperature: 0.8, // Moderate creativity for varied but consistent responses
    thinkingConfig: {
        thinkingBudget: 0 // Disable internal reasoning to save tokens
    },
    responseMimeType: 'application/json',
    responseSchema: {
        type: Type.OBJECT,
        required: ["word", "partOfSpeech", "definition", "charge", "difficulty", "hint1", "hint2"],
        properties: {
            word: {
                type: Type.STRING,
            },
            partOfSpeech: {
                type: Type.STRING,
            },
            definition: {
                type: Type.STRING,
            },
            charge: {
                type: Type.STRING,
            },
            difficulty: {
                type: Type.NUMBER,
            },
            hint1: {
                type: Type.STRING,
            },
            hint2: {
                type: Type.STRING,
            },
        },
    },
    // System instructions defining how AI should generate vocabulary card content
    systemInstruction: [
        {
            text: `You are an expert SAT vocabulary tutor.  
                You will be given a vocabulary word and its definition.  

                Return a JSON object with the following fields:  
                1. word: exactly as provided in the input.  
                2. partOfSpeech: deduced from the vocabulary word and definition (e.g., noun, verb, adjective, adverb).  
                3. definition: exactly as provided in the input.  
                4. charge: the connotation of the word (Neutral, Positive, or Negative only).  
                5. difficulty: an integer from 1 to 4 representing difficulty for SAT students.  
                6. hint1: a 60-word descriptive passage containing the vocabulary word. Wrap the vocabulary word in <b>...</b>.  
                7. hint2: an ~80-word passage with richer context. Wrap the vocabulary word in <b>...</b> and context clues in <mark style="background-color: #fff9c4">...</mark>.  

                Passage Structure:  
                - Topic must be Social Science, Natural Science, or Humanities.  
                - Refer to real books, studies, or scientific works (no fabrications).  
                - Except for human names, italicize other proper names with <i>...</i>.  
                - Include context clues like synonyms, contrasts, or paraphrases.  
                - Keep tone descriptive, not argumentative, and avoid overly complex vocabulary.  

                The output should be a JSON object containing the above fields.
            `
        }
    ],
};
// Use Gemini 2.5 Flash model for fast, cost-effective generation
const model = 'gemini-2.5-flash';

// Makes API call to Gemini AI to generate structured vocabulary card data
async function generateVocabData(word: string, definition: string) {
    const response = await geminiAI.models.generateContent({
        model,
        config,
        contents: `Word: ${word}. Definition: ${definition}`
    });
    let data = response.text;
    return data;
}