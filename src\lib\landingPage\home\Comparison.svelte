<script>
    import { H2, H3, P1, P2, P3 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    let {
        sectionLabel = "Comparison",
        headline = "There's a better way to study SAT",
        subheadline = "1600 and enjoying your life is not mutually exclusive",
        oldWayTitle = "Traditional Test Prep Center Sweatshops",
        oldWayDescription = "Classes with 200+ students squeezed into a room and taught by someone who doesn't care about your success.",
        newWayTitle = "Self-paced, Personalized Learning Platform",
        newWayDescription = "You decide when, where, and how you want to learn. All we do is motivate you. All the practice needed are right at your fingertips.",
        oldWayFeatures = [
            "Boring, overwhelming, and demotivating",
            "Zero personalization",
            "Use stolen questions (that you can find for free)",
            "Cost a shit ton of money (Up to $500/hr)",
        ],
        newWayFeatures = [
            "Make you study the SAT voluntarily",
            "Actionable personalized study plan",
            "Offer original questions that you cannot find anywhere else",
            "Easily affordable (actually, it's still free)"
        ]
    } = $props();
</script>

<SectionWrapper --bg-color="var(--aquamarine)" --padding-top="8rem" --padding-bottom="8rem">
<div class="comparison-section">
    <div class="comparison-header">
        <P2>{sectionLabel}</P2>
        <H2>{headline}</H2>
        <P1>{subheadline}</P1>
    </div>

    <div class="comparison-grid">
        <div class="comparison-card old-way">
            <div class="comparison-content">
                <H3>{oldWayTitle}</H3>
                <P2>{oldWayDescription}</P2>

                <div class="feature-list">
                    {#each oldWayFeatures as feature}
                        <div class="feature-item negative">
                            <div class="x-icon">✗</div>
                            <P3>{@html feature}</P3>
                        </div>
                    {/each}
                </div>
            </div>
        </div>

        <div class="comparison-card new-way">
            <div class="comparison-content">
                <H3>{newWayTitle}</H3>
                <P2>{newWayDescription}</P2>

                <div class="feature-list">
                    {#each newWayFeatures as feature}
                        <div class="feature-item positive">
                            <div class="check-icon">✓</div>
                            <P3>{@html feature}</P3>
                        </div>
                    {/each}
                </div>
            </div>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Comparison */
    .comparison-section {
                width: 100%;
        text-align: center;
    }
    
    .comparison-header {
        margin-bottom: 4rem;
    }
    
    .comparison-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
    
    .comparison-card {
        background: white;
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .comparison-card:hover {
        transform: translate(-0.25rem, -0.25rem);
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
    }

    .comparison-content {
        padding: 2rem;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        text-align: left;
    }
    
    .old-way {
        background: #ffebee;
    }
    
    .new-way {
        background: #e8f5e8;
    }
    
    .feature-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .check-icon {
        background: #4caf50;
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .x-icon {
        background: #f44336;
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .comparison-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
    }
</style>
