<script lang="ts">
	import Button from '$lib/ui/Button.svelte';
	import { H1, P1, P2, PopUp, Input, LoadingButton, HelpIcon, H3, H2 } from '$lib/ui';
	import type { Deck, VocabCard } from '$lib/types';
	import { browser } from '$app/environment';
	import { type Unsubscribe } from 'firebase/firestore';
	import { bulkAddCardsToDeck, calculateDeckStats, createDeck, subscribeToUserDecks, deleteDeck, removeCardFromDeck, updateDeck } from '$lib/firebase';
	import { onDestroy } from 'svelte';
	import { fly, fade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	
	let { data } = $props();

	// State for managing the pop-up
	let popUpState = $state<"Not Open" | "Add Deck" | "Edit Deck" | "Edit Cards" | "Add Cards" | "Delete Confirm" | "Select Definitions" | "Import Quizlet">("Not Open");
	let isPopupOpen = $derived(popUpState !== "Not Open");
	let isEditCardsOpen = $derived(popUpState === "Edit Cards");
	let isEditDeckOpen = $derived(popUpState === "Edit Deck");
	let isAddCardsOpen = $derived(popUpState === "Add Cards");
	let isAddDeckOpen = $derived(popUpState === "Add Deck");
	let isDeleteConfirmOpen = $derived(popUpState === "Delete Confirm");
	let isSelectDefinitionsOpen = $derived(popUpState === "Select Definitions");
	let isImportQuizletOpen = $derived(popUpState === "Import Quizlet");

	// State for managing the deck creation form
	let deckName = $state('');
	let deckDescription = $state('');
	$effect(() => {
		if (!isAddDeckOpen) {
			deckDescription = '';
			deckName = '';
		}
	})

	// State for managing the deck editing form
	let editDeckName = $state('');
	let editDeckDescription = $state('');
	let isUpdatingDeck = $state(false);
	$effect(() => {
		if (!isEditDeckOpen) {
			editDeckName = '';
			editDeckDescription = '';
		}
	})

	// State for managing Quizlet import
	let quizletUrl = $state('');
	let isImportingQuizlet = $state(false);
	let quizletVocabularies = $state<Array<{word: string, POS: string, definition: string}> | null>(null);
	let importDeckName = $state('');
	let importDeckDescription = $state('');
	let isCreatingDeck = $state(false);
	$effect(() => {
		if (!isImportQuizletOpen) {
			quizletUrl = '';
			quizletVocabularies = null;
			importDeckName = '';
			importDeckDescription = '';
		}
	})

	// Unified message system
	let popUpMessage = $state('');
	let messageState = $state<'error' | 'success' | 'info'>('info');

	// Function to set message with state
	function setMessage(message: string, state: 'error' | 'success' | 'info' = 'info') {
		popUpMessage = message;
		messageState = state;
	}


	// State for managing decks and Firebase subscription
	let decks = $state<Deck[] | null>(null);
	let currentDeckIndex = $state(null);
	let currentDeckData = $derived<Deck | null>(decks && currentDeckIndex != null ? decks[currentDeckIndex] : null);
	let deckToDelete = $state<Deck | null>(null);
	
	let unsubscriberUserDecks = $state<Unsubscribe | null>(null);
	
	// Initialize deck subscriptions when component mounts
	if (data.uid && browser) {
		unsubscriberUserDecks = subscribeToUserDecks(data.uid, (d) => {
			decks = d;
		});
	}

	// Clean up the listener when the component is destroyed
	onDestroy(() => {
		unsubscriberUserDecks?.();
	});


	// State for editing cards
	let searchQuery = $state('');
	let filteredCards = $derived(
		currentDeckData?.cards.filter(card => 
			card.vocabCard.word.toString().includes(searchQuery.toLowerCase())
		) || []
	);

	// State for adding cards
	let cardName = $state('');
	let wordDefinitions = $state<Array<{POS: string, definitions: string[]}> | null>(null);
	let selectedDefinition = $state<string | null>(null);
	let currentWord = $state('');
	let activePOSTab = $state<string>('');

	// State for multiple words
	let multipleWords = $state<Array<{
		word: string,
		definitions: Array<{POS: string, definitions: string[]}>,
		selectedDefinition: string
	}> | null>(null);
	let currentWordIndex = $state<number | null>(null);
	
	$effect(() => {
		if (!isEditCardsOpen) {
			searchQuery = '';
		}
		if (!isAddCardsOpen && !isSelectDefinitionsOpen) {
			// Only reset when completely closing the add cards workflow
			cardName = '';
			multipleWords = null;
			currentWordIndex = null;
		}
		if (!isSelectDefinitionsOpen) {
			wordDefinitions = null;
			selectedDefinition = null;
			currentWord = '';
			activePOSTab = '';
			// Don't reset currentWordIndex here as it's needed for navigation
		}
		if (!isPopupOpen) {
			// Reset message when all popups are closed
			popUpMessage = '';
			messageState = 'info';
		}
	})

	// Set the first POS as active when wordDefinitions change
	$effect(() => {
		if (wordDefinitions && wordDefinitions.length > 0 && !activePOSTab) {
			activePOSTab = wordDefinitions[0].POS;
		}
	})

	function handleDelete(deck: Deck) {
		deckToDelete = deck;
		popUpState = "Delete Confirm";
	}

	async function confirmDelete() {
		closePopUp();
		if (deckToDelete) {
			await deleteDeck(data.uid, deckToDelete.id);
			deckToDelete = null;
		}
	}

	function openAddDeck() {
		popUpState = "Add Deck";
	}

	function openImportQuizlet() {
		popUpState = "Import Quizlet";
	}

	function openEditDeck() {
		if (currentDeckData) {
			editDeckName = currentDeckData.name;
			editDeckDescription = currentDeckData.description;
			popUpState = "Edit Deck";
		}
	}

	// Function to update deck name and description
	async function updateDeckInfo() {
		if (!currentDeckData || !editDeckName.trim()) {
			setMessage('Please enter a deck name', 'error');
			return;
		}

		isUpdatingDeck = true;
		setMessage('Updating deck...', 'info');

		try {
			await updateDeck(data.uid, currentDeckData.id, {
				name: editDeckName.trim(),
				description: editDeckDescription.trim()
			});

			setMessage('Deck updated successfully!', 'success');

			// Show notification
			notificationMessage = `Deck "${editDeckName.trim()}" updated successfully!`;
			showNotification = true;

			// Hide notification after 3 seconds
			setTimeout(() => {
				showNotification = false;
			}, 3000);

			closePopUp();

		} catch (error) {
			console.error('Error updating deck:', error);
			setMessage('An error occurred while updating the deck. Please try again.', 'error');
		} finally {
			isUpdatingDeck = false;
		}
	}

	function closePopUp() {
		popUpState = "Not Open";
		setMessage('', 'info');
	}

	function closeDeck() {
		currentDeckIndex = null;
	}

	function openEditCards() {
		popUpState = "Edit Cards";
	}

	function openAddCards() {
		// Initialize with empty word list
		multipleWords = [];
		popUpState = "Add Cards";
	}

	// Add this function to handle the add deck form submission
	async function handleAddDeckFormSubmit(e: Event) {
		// Prevent default form submission behavior
		e.preventDefault();
		
		// Check if the deck name is empty
		if (!deckName.trim()) {
			alert('Please enter a deck name');
			return;
		}

		// Create the deck
		await createDeck(data.uid, deckName.trim(), deckDescription.trim());
		closePopUp();
	}

	function handleFetchDeck(deckIndex: number) {
		currentDeckIndex = deckIndex
	}

	let isAddingCard = $state(false);
	let isFetchingWordData = $state(false);

	// Notification state
	let showNotification = $state(false);
	let notificationMessage = $state('');



	// Function to select definition (single selection)
	function selectDefinition(definition: string) {
		selectedDefinition = definition;
	}

	// Function to check if a definition is selected
	function isDefinitionSelected(definition: string) {
		return selectedDefinition === definition;
	}

	// Function to open definition selection for a specific word
	function openDefinitionSelection(wordIndex: number) {
		if (multipleWords && multipleWords[wordIndex]) {
			currentWordIndex = wordIndex;
			currentWord = multipleWords[wordIndex].word;
			wordDefinitions = multipleWords[wordIndex].definitions;
			selectedDefinition = multipleWords[wordIndex].selectedDefinition;

			// Set active POS tab to the one that contains the current definition, or first one if no current definition
			if (wordDefinitions && wordDefinitions.length > 0) {
				if (selectedDefinition) {
					// Find which POS group contains the current selected definition
					const posWithCurrentDefinition = wordDefinitions.find(posGroup =>
						posGroup.definitions.includes(selectedDefinition)
					);
					activePOSTab = posWithCurrentDefinition ? posWithCurrentDefinition.POS : wordDefinitions[0].POS;
				} else {
					// No current definition, default to first POS
					activePOSTab = wordDefinitions[0].POS;
				}
			}

			popUpState = "Select Definitions";
		}
	}


	async function getDictionaryData(word) {
		const dictionaryApiResponse = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`)
		const dictionaryData = await dictionaryApiResponse.json();

		if (dictionaryData.title === "No Definitions Found") {
			return [];
		}

		const extractedData: {POS: string, definitions: string[]}[] = Object.values(
		dictionaryData
			.map(wordEntry => wordEntry.meanings)
			.flat()
			.reduce((acc, posGroup) => {
				const pos = posGroup.partOfSpeech;
				if (!acc[pos]) {
					acc[pos] = { POS: pos, definitions: [] };
				}
				acc[pos].definitions.push(
					...posGroup.definitions.map(def => def.definition.trim())
				);
				return acc;
			}, {})
		);
		
		return extractedData;
	}


	// Function to add words to the list (supports single word or comma-separated words)
	async function addWords() {
		if (!cardName.trim()) {
			setMessage('Please enter a word or words', 'error');
			return;
		}

		// Split by comma and clean up words
		const inputWords = cardName.split(',')
			.map(w => w.trim())
			.filter(w => w.length > 0);

		if (inputWords.length === 0) {
			setMessage('Please enter valid words', 'error');
			return;
		}

		// Check for duplicates in existing list
		const existingWords = multipleWords?.map(w => w.word.toLowerCase()) || [];
		const newWords = inputWords.filter(word => !existingWords.includes(word.toLowerCase()));

		if (newWords.length === 0) {
			setMessage('All words are already in the list', 'error');
			return;
		}

		if (newWords.length < inputWords.length) {
			const duplicates = inputWords.filter(word => existingWords.includes(word.toLowerCase()));
			setMessage(`Skipping duplicate words: ${duplicates.join(', ')}. Adding ${newWords.length} new words...`, 'info');
		} else {
			setMessage(`Fetching definitions for ${newWords.length} word(s)...`, 'info');
		}

		isFetchingWordData = true;

		try {
			// Fetch definitions for all words in parallel using Promise.all
			const fetchPromises = newWords.map(async (word) => {
				try {
					const dictionaryData = await getDictionaryData(word);

					// Select the first definition from the first POS as default
					const defaultDefinition = dictionaryData[0]?.definitions[0] || '';

					return {
						word: word,
						definitions: dictionaryData,
						selectedDefinition: defaultDefinition
					};
				} catch (error) {
					console.error(`Error fetching definitions for "${word}":`, error);
					// Return word with no definitions on error
					return {
						word: word,
						definitions: [],
						selectedDefinition: ''
					};
				}
			});

			// Wait for all fetch operations to complete
			const wordResults = await Promise.all(fetchPromises);

			// Filter out words with no definitions
			const successfulWords = wordResults.filter(w => w.definitions.length > 0);
			const failedWords = wordResults.filter(w => w.definitions.length === 0);

			// Only add words that have definitions to the list
			if (successfulWords.length > 0) {
				if (!multipleWords) {
					multipleWords = successfulWords;
				} else {
					multipleWords = [...multipleWords, ...successfulWords];
				}
			}

			// Clear input and show appropriate message
			cardName = '';

			if (failedWords.length === 0) {
				setMessage('', 'success');
			} else {
				setMessage(`No definitions found for: ${failedWords.map(w => w.word).join(', ')}`, 'error');
			}

		} catch (error) {
			console.error('Error fetching word definitions:', error);
			setMessage('An error occurred while fetching definitions', 'error');
		} finally {
			isFetchingWordData = false;
		}
	}

	// Function to remove a word from the list
	function removeWordFromList(wordIndex: number) {
		if (multipleWords && wordIndex >= 0 && wordIndex < multipleWords.length) {
			multipleWords = multipleWords.filter((_, index) => index !== wordIndex);
		}
	}


	// Function to add all cards from multiple words
	async function addAllCards() {
		if (!multipleWords || !currentDeckData) {
			return;
		}

		isAddingCard = true;
		setMessage('', 'info');

		const newWords = multipleWords
		.map(w => ({
			word: w.word.trim(),
			definition: w.selectedDefinition.trim()
		}))
		.filter(wordData =>
			!currentDeckData.cards.find(card =>
			card.vocabCard.word.toLowerCase() === wordData.word.toLowerCase() &&
			card.vocabCard.definition === wordData.definition
			)
		)

		try {
			const response = await fetch('/api/vocab-tool/generate-vocab-data', {
				method: 'POST',
				body: JSON.stringify({
					uid: data.uid,
					wordList: newWords,
					isQuizletImport: false
				})
			});

			const { message: msg, success, vocabCards } = await response.json();

			if (!success) {
				setMessage(msg || 'An error occurred while creating cards.', 'error');
				isAddingCard = false;
				return;
			}

			await bulkAddCardsToDeck(data.uid, currentDeckData.id, vocabCards);

			// Show notification
			if (vocabCards.length > 0) {
				notificationMessage = `Successfully added ${vocabCards.length} cards!`;
				showNotification = true;

				// Hide notification after 3 seconds
				setTimeout(() => {
					showNotification = false;
				}, 3000);
			}

			// Reset state
			multipleWords = null;
			cardName = '';
			closePopUp();

		} catch (error) {
			setMessage('An error occurred while creating cards.', 'error');
			console.error('Error adding cards:', error);
		} finally {
			isAddingCard = false;
		}
	}


	// Function to handle card removal
	async function removeCard(cardId: number) {
		if (!currentDeckData) return;
		await removeCardFromDeck(data.uid, currentDeckData.id, cardId);
	}


	// Function to fetch Quizlet data
	async function fetchQuizletData() {
		if (!quizletUrl.trim()) {
			setMessage('Please enter a Quizlet URL', 'error');
			return;
		}

		isImportingQuizlet = true;
		setMessage('Importing from Quizlet...', 'info');

		try {
			// Get vocabulary list from Quizlet
			const quizletResponse = await fetch('/api/vocab-tool/get-quizlet-data', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ url: quizletUrl.trim() })
			});

			const quizletResult = await quizletResponse.json();

			if (!quizletResult.success) {
				setMessage(quizletResult.message || 'Failed to import data from Quizlet', 'error');
				isImportingQuizlet = false;
				return;
			}

			const vocabularies = quizletResult.data;
			if (!vocabularies || vocabularies.length === 0) {
				setMessage('No vocabulary found in the Quizlet set', 'error');
				isImportingQuizlet = false;
				return;
			}

			// Store vocabularies and show deck creation form
			quizletVocabularies = vocabularies;
			setMessage('', 'info');
			isImportingQuizlet = false;

		} catch (error) {
			console.error('Error fetching Quizlet data:', error);
			setMessage('An error occurred while fetching from Quizlet. Please try again.', 'error');
			isImportingQuizlet = false;
		}
	}

	// Function to create deck and import vocabularies
	async function createDeckQuizlet() {
		if (!importDeckName.trim()) {
			setMessage('Please enter a deck name', 'error');
			return;
		}

		isCreatingDeck = true;
		setMessage('Creating deck...', 'info');
		let newDeckId: string | null = null;

		try {
			// Create the deck
			newDeckId = await createDeck(data.uid, importDeckName.trim(), importDeckDescription.trim());

			if (!newDeckId) {
				setMessage('Failed to create deck', 'error');
				isCreatingDeck = false;
				return;
			}

			setMessage(`Deck created! Processing ${quizletVocabularies.length} vocabulary items...`, 'info');

			try {
				// Generate vocab data
				const vocabDataResponse = await fetch('/api/vocab-tool/generate-vocab-data', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						uid: data.uid,
						wordList: quizletVocabularies,
						isQuizletImport: true
					})
				});

				const { message: msg, success, vocabCards } = await vocabDataResponse.json();

				if (!success) {
					// Delete the deck since vocab generation failed
					await deleteDeck(data.uid, newDeckId);
					setMessage(msg || 'Failed to generate vocab data. Deck has been removed.', 'error');
					isCreatingDeck = false;
					return;
				}

				await bulkAddCardsToDeck(data.uid, newDeckId, vocabCards);

				// Show final results
				setMessage(`Import completed!`, 'success');

				// Show notification
				notificationMessage = `Successfully created deck "${importDeckName}" with ${vocabCards.length} cards from Quizlet!`;
				showNotification = true;

				closePopUp();

				// Hide notification after 3 seconds
				setTimeout(() => {
					showNotification = false;
				}, 3000);
			} catch (error) {
				console.error('Error during vocab processing or card addition:', error);
				// Delete the deck since processing failed
				if (newDeckId) {
					await deleteDeck(data.uid, newDeckId);
				}
				setMessage('Failed to process vocabulary data. Deck has been removed.', 'error');
				isCreatingDeck = false;
				return;
			}
		} catch (error) {
			console.error('Error during deck creation and import:', error);
			// If deck was created but something else failed, delete it
			if (newDeckId) {
				try {
					await deleteDeck(data.uid, newDeckId);
					setMessage('An error occurred during import. Deck has been removed.', 'error');
				} catch (deleteError) {
					console.error('Error deleting deck after failure:', deleteError);
					setMessage('An error occurred during import. Please manually delete the created deck.', 'error');
				}
			} else {
				setMessage('An error occurred while creating the deck. Please try again.', 'error');
			}
		} finally {
			isCreatingDeck = false;
		}
	}
</script>

<svelte:head>
	<title>DSAT16 - Vocab Tool</title>
</svelte:head>

<div class="decks-bg">
	<header class="decks-header">
		<H1>VOCAB16</H1>
	</header>
	<main class="decks-main">
		<section class="decks-card">
			{#if currentDeckData}
				{@const stats = calculateDeckStats(currentDeckData.cards)}
				<div class="deck-header">
					<button onclick={() => closeDeck()} aria-label="Close deck">
						<svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M8.29 11.29C8.19896 11.3851 8.12759 11.4972 8.08 11.62C7.97998 11.8635 7.97998 12.1365 8.08 12.38C8.12759 12.5028 8.19896 12.6149 8.29 12.71L11.29 15.71C11.4783 15.8983 11.7337 16.0041 12 16.0041C12.2663 16.0041 12.5217 15.8983 12.71 15.71C12.8983 15.5217 13.0041 15.2663 13.0041 15C13.0041 14.7337 12.8983 14.4783 12.71 14.29L11.41 13H15C15.2652 13 15.5196 12.8946 15.7071 12.7071C15.8946 12.5196 16 12.2652 16 12C16 11.7348 15.8946 11.4804 15.7071 11.2929C15.5196 11.1054 15.2652 11 15 11H11.41L12.71 9.71C12.8037 9.61704 12.8781 9.50644 12.9289 9.38458C12.9797 9.26272 13.0058 9.13201 13.0058 9C13.0058 8.86799 12.9797 8.73728 12.9289 8.61542C12.8781 8.49356 12.8037 8.38296 12.71 8.29C12.617 8.19627 12.5064 8.12188 12.3846 8.07111C12.2627 8.02034 12.132 7.9942 12 7.9942C11.868 7.9942 11.7373 8.02034 11.6154 8.07111C11.4936 8.12188 11.383 8.19627 11.29 8.29L8.29 11.29ZM2 12C2 13.9778 2.58649 15.9112 3.6853 17.5557C4.78412 19.2002 6.3459 20.4819 8.17317 21.2388C10.0004 21.9957 12.0111 22.1937 13.9509 21.8079C15.8907 21.422 17.6725 20.4696 19.0711 19.0711C20.4696 17.6725 21.422 15.8907 21.8079 13.9509C22.1937 12.0111 21.9957 10.0004 21.2388 8.17317C20.4819 6.3459 19.2002 4.78412 17.5557 3.6853C15.9112 2.58649 13.9778 2 12 2C10.6868 2 9.38642 2.25866 8.17317 2.7612C6.95991 3.26375 5.85752 4.00035 4.92893 4.92893C3.05357 6.8043 2 9.34784 2 12ZM20 12C20 13.5823 19.5308 15.129 18.6518 16.4446C17.7727 17.7602 16.5233 18.7855 15.0615 19.391C13.5997 19.9965 11.9911 20.155 10.4393 19.8463C8.88743 19.5376 7.46197 18.7757 6.34315 17.6569C5.22433 16.538 4.4624 15.1126 4.15372 13.5607C3.84504 12.0089 4.00346 10.4003 4.60896 8.93853C5.21447 7.47672 6.23984 6.22729 7.55544 5.34824C8.87103 4.46919 10.4177 4 12 4C14.1217 4 16.1566 4.84285 17.6569 6.34315C19.1571 7.84344 20 9.87827 20 12Z" fill="black"/>
						</svg>			
					</button>
					<div class="flex flex-row gap-4 items-center"> 
					<P1 isBold={true}>{currentDeckData.name}</P1>
					<button aria-label="Edit deck" onclick={openEditDeck}>
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M21.32 9.55L19.43 8.92L20.32 7.14C20.4102 6.95369 20.4404 6.74397 20.4064 6.53978C20.3723 6.33558 20.2758 6.14699 20.13 6L18 3.87C17.8522 3.72209 17.6618 3.62421 17.4555 3.59013C17.2493 3.55605 17.0375 3.58748 16.85 3.68L15.07 4.57L14.44 2.68C14.3735 2.483 14.2472 2.31163 14.0787 2.18975C13.9102 2.06787 13.7079 2.00155 13.5 2H10.5C10.2904 1.99946 10.0858 2.06482 9.91537 2.18685C9.7449 2.30887 9.61709 2.48138 9.55 2.68L8.92 4.57L7.14 3.68C6.95369 3.58978 6.74397 3.55961 6.53978 3.59364C6.33558 3.62767 6.14699 3.72423 6 3.87L3.87 6C3.72209 6.14777 3.62421 6.33818 3.59013 6.54446C3.55605 6.75074 3.58748 6.96251 3.68 7.15L4.57 8.93L2.68 9.56C2.483 9.62654 2.31163 9.75283 2.18975 9.92131C2.06787 10.0898 2.00155 10.2921 2 10.5V13.5C1.99946 13.7096 2.06482 13.9142 2.18685 14.0846C2.30887 14.2551 2.48138 14.3829 2.68 14.45L4.57 15.08L3.68 16.86C3.58978 17.0463 3.55961 17.256 3.59364 17.4602C3.62767 17.6644 3.72423 17.853 3.87 18L6 20.13C6.14777 20.2779 6.33818 20.3758 6.54446 20.4099C6.75074 20.444 6.96251 20.4125 7.15 20.32L8.93 19.43L9.56 21.32C9.62709 21.5186 9.7549 21.6911 9.92537 21.8132C10.0958 21.9352 10.3004 22.0005 10.51 22H13.51C13.7196 22.0005 13.9242 21.9352 14.0946 21.8132C14.2651 21.6911 14.3929 21.5186 14.46 21.32L15.09 19.43L16.87 20.32C17.0551 20.4079 17.2628 20.4369 17.4649 20.4029C17.667 20.3689 17.8538 20.2737 18 20.13L20.13 18C20.2779 17.8522 20.3758 17.6618 20.4099 17.4555C20.444 17.2493 20.4125 17.0375 20.32 16.85L19.43 15.07L21.32 14.44C21.517 14.3735 21.6884 14.2472 21.8103 14.0787C21.9321 13.9102 21.9985 13.7079 22 13.5V10.5C22.0005 10.2904 21.9352 10.0858 21.8132 9.91537C21.6911 9.7449 21.5186 9.61709 21.32 9.55ZM20 12.78L18.8 13.18C18.5241 13.2695 18.2709 13.418 18.0581 13.6151C17.8452 13.8122 17.6778 14.0533 17.5675 14.3216C17.4571 14.5899 17.4064 14.879 17.419 15.1688C17.4315 15.4586 17.5069 15.7422 17.64 16L18.21 17.14L17.11 18.24L16 17.64C15.7436 17.5122 15.4627 17.4411 15.1763 17.4313C14.89 17.4215 14.6049 17.4734 14.3403 17.5834C14.0758 17.6934 13.8379 17.8589 13.6429 18.0688C13.4479 18.2787 13.3003 18.5281 13.21 18.8L12.81 20H11.22L10.82 18.8C10.7305 18.5241 10.582 18.2709 10.3849 18.0581C10.1878 17.8452 9.94671 17.6778 9.67842 17.5675C9.41014 17.4571 9.12105 17.4064 8.83123 17.419C8.5414 17.4315 8.25777 17.5069 8 17.64L6.86 18.21L5.76 17.11L6.36 16C6.4931 15.7422 6.56852 15.4586 6.58105 15.1688C6.59358 14.879 6.5429 14.5899 6.43254 14.3216C6.32218 14.0533 6.15478 13.8122 5.94195 13.6151C5.72912 13.418 5.47595 13.2695 5.2 13.18L4 12.78V11.22L5.2 10.82C5.47595 10.7305 5.72912 10.582 5.94195 10.3849C6.15478 10.1878 6.32218 9.94671 6.43254 9.67842C6.5429 9.41014 6.59358 9.12105 6.58105 8.83123C6.56852 8.5414 6.4931 8.25777 6.36 8L5.79 6.89L6.89 5.79L8 6.36C8.25777 6.4931 8.5414 6.56852 8.83123 6.58105C9.12105 6.59358 9.41014 6.5429 9.67842 6.43254C9.94671 6.32218 10.1878 6.15478 10.3849 5.94195C10.582 5.72912 10.7305 5.47595 10.82 5.2L11.22 4H12.78L13.18 5.2C13.2695 5.47595 13.418 5.72912 13.6151 5.94195C13.8122 6.15478 14.0533 6.32218 14.3216 6.43254C14.5899 6.5429 14.879 6.59358 15.1688 6.58105C15.4586 6.56852 15.7422 6.4931 16 6.36L17.14 5.79L18.24 6.89L17.64 8C17.5122 8.25645 17.4411 8.53735 17.4313 8.82369C17.4215 9.11003 17.4734 9.39513 17.5834 9.65969C17.6934 9.92424 17.8589 10.1621 18.0688 10.3571C18.2787 10.5521 18.5281 10.6997 18.8 10.79L20 11.19V12.78ZM12 8C11.2089 8 10.4355 8.2346 9.77772 8.67413C9.11993 9.11365 8.60724 9.73836 8.30448 10.4693C8.00173 11.2002 7.92252 12.0044 8.07686 12.7804C8.2312 13.5563 8.61217 14.269 9.17158 14.8284C9.73099 15.3878 10.4437 15.7688 11.2196 15.9231C11.9956 16.0775 12.7998 15.9983 13.5307 15.6955C14.2616 15.3928 14.8864 14.8801 15.3259 14.2223C15.7654 13.5645 16 12.7911 16 12C16 10.9391 15.5786 9.92172 14.8284 9.17158C14.0783 8.42143 13.0609 8 12 8ZM12 14C11.6044 14 11.2178 13.8827 10.8889 13.6629C10.56 13.4432 10.3036 13.1308 10.1522 12.7654C10.0009 12.3999 9.96126 11.9978 10.0384 11.6098C10.1156 11.2219 10.3061 10.8655 10.5858 10.5858C10.8655 10.3061 11.2219 10.1156 11.6098 10.0384C11.9978 9.96126 12.3999 10.0009 12.7654 10.1522C13.1308 10.3036 13.4432 10.56 13.6629 10.8889C13.8827 11.2178 14 11.6044 14 12C14 12.5304 13.7893 13.0391 13.4142 13.4142C13.0391 13.7893 12.5304 14 12 14Z" fill="black"/>
						</svg>
					</button>
					</div>
					<div style="width: 24px"></div>
				</div>
				<div class="deck-stats-description">
					<P2>{currentDeckData.description || 'No description provided.'}</P2>
				</div>
				<div class="deck-stats-list">
					<div class="deck-stat-row">
						<P2 isBold={true}>Total Cards:</P2>
						<P2>{stats.total}</P2>
					</div>
					<div class="deck-stat-row" style="--text-color: var(--aquamarine)">
						<P2 isBold={true}>
							<HelpIcon --bg-color=var(--text-color) title="Words you have not encountered">New</HelpIcon>
						</P2>
						<P2>{stats.new}</P2>
					</div>
					<div class="deck-stat-row" style="--text-color: var(--sky-blue)">
						<P2 isBold={true}>
							<HelpIcon --bg-color=var(--text-color) title="Words you are currently learning">(Re)Learning</HelpIcon>
						</P2>
						<P2>{stats.learn}</P2>
					</div>
					<div class="deck-stat-row" style="--text-color: var(--tangerine)">
						<P2 isBold={true} >
							<HelpIcon --bg-color=var(--text-color) title="Words you've learned">Reviewing</HelpIcon>
						</P2>
						<P2>{stats.review}</P2>
					</div>
					<div class="deck-stat-row" style="--text-color: var(--rose)">
						<P2 isBold={true}>
							<HelpIcon --bg-color=var(--text-color) title="Words scheduled for review today">Due</HelpIcon>
						</P2>
						<P2>{stats.due}</P2>
					</div>
				</div>
				<div class="deck-stats-actions">
					<a href="/study/vocab-tool/{currentDeckData.id}"><Button>Start</Button></a>
					<Button --button-bg-color=var(--aquamarine) onclick={openAddCards}>Add Cards</Button>
					<Button --button-bg-color=var(--tangerine) onclick={openEditCards}>Edit Cards</Button>
				</div>
			{:else}
				<div class="decks-list-header">
					<div class="deck-name-col"><P1 isBold={true}>Deck</P1></div>
					<div class="stat-col"><P1 isBold={true} --text-color="var(--rose)"><HelpIcon title="Words to review soon">Due</HelpIcon></P1></div>
					<div class="stat-col"><P1 isBold={true}><HelpIcon title="The total number of words in the deck">Total</HelpIcon></P1></div>
					<div class="remove-col"></div>
				</div>
				<div class="decks-list-body">
				{#if decks === null }
					<div class="loading-container">
						<div class="loading-spinner"></div>
						<P2>Loading your decks...</P2>
					</div>
				{:else if decks.length === 0}
					<div class="empty-state">
						<P2>No decks yet. Create your first deck to get started!</P2>
					</div>
				{:else}
					{#each decks as deck, deckIndex}
						{@const stats = calculateDeckStats(deck.cards)}
						<div role="button" class="decks-list-row" tabindex="0" onclick={() => handleFetchDeck(deckIndex)} onkeydown={() => {}}>
							<div class="deck-name-col" ><P2 isBold={true}>{deck.name}</P2></div>
							<div class="stat-col"><P2 --text-color="var(--rose)">{stats.due}</P2></div>
							<div class="stat-col"><P2>{stats.total}</P2></div>
							<button
								class="delete-btn" 
								aria-label="Delete deck" 
								tabindex="0" 
								onclick={(e) => { e.stopPropagation(); handleDelete(deck);}}
							>
								<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M10 18C10.2652 18 10.5196 17.8946 10.7071 17.7071C10.8946 17.5196 11 17.2652 11 17V11C11 10.7348 10.8946 10.4804 10.7071 10.2929C10.5196 10.1054 10.2652 10 10 10C9.73478 10 9.48043 10.1054 9.29289 10.2929C9.10536 10.4804 9 10.7348 9 11V17C9 17.2652 9.10536 17.5196 9.29289 17.7071C9.48043 17.8946 9.73478 18 10 18ZM20 6H16V5C16 4.20435 15.6839 3.44129 15.1213 2.87868C14.5587 2.31607 13.7956 2 13 2H11C10.2044 2 9.44129 2.31607 8.87868 2.87868C8.31607 3.44129 8 4.20435 8 5V6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7C3 7.26522 3.10536 7.51957 3.29289 7.70711C3.48043 7.89464 3.73478 8 4 8H5V19C5 19.7956 5.31607 20.5587 5.87868 21.1213C6.44129 21.6839 7.20435 22 8 22H16C16.7956 22 17.5587 21.6839 18.1213 21.1213C18.6839 20.5587 19 19.7956 19 19V8H20C20.2652 8 20.5196 7.89464 20.7071 7.70711C20.8946 7.51957 21 7.26522 21 7C21 6.73478 20.8946 6.48043 20.7071 6.29289C20.5196 6.10536 20.2652 6 20 6ZM10 5C10 4.73478 10.1054 4.48043 10.2929 4.29289C10.4804 4.10536 10.7348 4 11 4H13C13.2652 4 13.5196 4.10536 13.7071 4.29289C13.8946 4.48043 14 4.73478 14 5V6H10V5ZM17 19C17 19.2652 16.8946 19.5196 16.7071 19.7071C16.5196 19.8946 16.2652 20 16 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V8H17V19ZM14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18Z" fill="black"/>
								</svg>

							</button>
						</div>
					{/each}
				{/if}
					<div class="decks-actions">
						<Button --button-bg-color="var(--sky-blue)" onclick={openAddDeck}>Create Deck</Button>
						<Button --button-bg-color="var(--aquamarine)" onclick={openImportQuizlet}>Import from Quizlet</Button>
					</div>
				</div>
			{/if}
		</section>
	</main>
</div>

<PopUp --popup-color="var(--light-tangerine)" --close-button-color="var(--tangerine)" bind:isOpen={isPopupOpen} size={isSelectDefinitionsOpen ? "very-large" : "large"} onclose={closePopUp}>
	{#if isAddDeckOpen}
	<form class="add-deck-popup-content" onsubmit={handleAddDeckFormSubmit}>
		<H2 --text-align=center>Create A New Deck</H2>
		<Input
			label="Deck name:"
			id="deck-name"
			placeholder="Enter deck name"
			required
			fullWidth
			bind:value={deckName}
		/>
		<Input
			label="Description:"
			id="deck-desc"
			placeholder="Enter description"
			fullWidth
			bind:value={deckDescription}
		/>
		<div class="add-deck-actions">
			<LoadingButton type="submit">Create</LoadingButton>
			<Button type="button" isSecondary onclick={() => {
				closePopUp();
				deckToDelete = null;
			}}>Cancel</Button>
		</div>
	</form>
	{:else if isEditDeckOpen}
	<form class="edit-deck-popup-content" onsubmit={(e) => { e.preventDefault(); updateDeckInfo(); }}>
		<H2 --text-align=center>Edit Deck</H2>
		<Input
			label="Deck name:"
			id="edit-deck-name"
			placeholder="Enter deck name"
			required
			fullWidth
			bind:value={editDeckName}
			onkeydown={(e) => e.key === 'Enter' && !isUpdatingDeck && updateDeckInfo()}
		/>
		<Input
			label="Description:"
			id="edit-deck-desc"
			placeholder="Enter description"
			fullWidth
			bind:value={editDeckDescription}
			onkeydown={(e) => e.key === 'Enter' && !isUpdatingDeck && updateDeckInfo()}
		/>
		<div class="add-deck-actions">
			<Button type="submit" disabled={isUpdatingDeck}>
				{isUpdatingDeck ? 'Updating...' : 'Update Deck'}
			</Button>
			<Button type="button" isSecondary onclick={closePopUp}>Cancel</Button>
		</div>
	</form>
	{:else if isEditCardsOpen}
	<div class="edit-cards-popup-content flex flex-col gap-4">
		<H2 --text-align=center>Edit Cards in {currentDeckData?.name}</H2>
		<div class="search-container">
			<Input 
				bind:value={searchQuery} 
				id="search-cards" 
				placeholder="Search vocab..." 
				fullWidth
			/>
		</div>
		<div class="cards-list flex flex-col bg-white max-h-[40vh] overflow-y-auto mt-2 border-2 border-black border-solid rounded-lg">
			{#if filteredCards.length === 0}
			<div class="p-4">
				<P2 --text-align=center>
					{searchQuery ? 'No cards match your search.' : 'No cards in this deck yet.'}
				</P2>
			</div>
			{:else}
				{#each filteredCards as card (card.id)}
					<div
						out:fade={{ duration: 300, easing: quintOut }}
						class="card-item flex flex-row justify-between p-4"
					>
						<div class="card-content">
							<div class="card-word flex flex-row gap-2 items-center">
								<P1 isBold={true}>{card.vocabCard.word}</P1>
								<p>({card.vocabCard.partOfSpeech})</p>
							</div>
						</div>
						<button 
							class="remove-card-btn" 
							onclick={() => removeCard(card.id)}
							aria-label="Remove card"
						>
							<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M10 18C10.2652 18 10.5196 17.8946 10.7071 17.7071C10.8946 17.5196 11 17.2652 11 17V11C11 10.7348 10.8946 10.4804 10.7071 10.2929C10.5196 10.1054 10.2652 10 10 10C9.73478 10 9.48043 10.1054 9.29289 10.2929C9.10536 10.4804 9 10.7348 9 11V17C9 17.2652 9.10536 17.5196 9.29289 17.7071C9.48043 17.8946 9.73478 18 10 18ZM20 6H16V5C16 4.20435 15.6839 3.44129 15.1213 2.87868C14.5587 2.31607 13.7956 2 13 2H11C10.2044 2 9.44129 2.31607 8.87868 2.87868C8.31607 3.44129 8 4.20435 8 5V6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7C3 7.26522 3.10536 7.51957 3.29289 7.70711C3.48043 7.89464 3.73478 8 4 8H5V19C5 19.7956 5.31607 20.5587 5.87868 21.1213C6.44129 21.6839 7.20435 22 8 22H16C16.7956 22 17.5587 21.6839 18.1213 21.1213C18.6839 20.5587 19 19.7956 19 19V8H20C20.2652 8 20.5196 7.89464 20.7071 7.70711C20.8946 7.51957 21 7.26522 21 7C21 6.73478 20.8946 6.48043 20.7071 6.29289C20.5196 6.10536 20.2652 6 20 6ZM10 5C10 4.73478 10.1054 4.48043 10.2929 4.29289C10.4804 4.10536 10.7348 4 11 4H13C13.2652 4 13.5196 4.10536 13.7071 4.29289C13.8946 4.48043 14 4.73478 14 5V6H10V5ZM17 19C17 19.2652 16.8946 19.5196 16.7071 19.7071C16.5196 19.8946 16.2652 20 16 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V8H17V19ZM14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18Z" fill="#dc2626"/>
							</svg>
						</button>
					</div>
				{/each}
			{/if}
		</div>
	</div>
	{:else if isAddCardsOpen}
	<div class="add-cards-popup-content flex flex-col items-center">
		<H2 --text-align=center>Add Cards To Deck</H2>

		<!-- Add single word input -->
		<div class="border-b-2 border-gray-800 pb-4 mb-4 flex flex-col gap-4 items-center mt-5 w-[600px]">
			<div class="add-card-vocab-name w-full">
				<Input bind:value={cardName} id="card-name" placeholder="Enter words (comma-separated)..." required fullWidth onkeydown={(e) => e.key === 'Enter' && !isFetchingWordData && addWords()}>
					{#if !isFetchingWordData}
						<Button onclick={addWords} disabled={isFetchingWordData}>
							Add Words
						</Button>
					{:else}
						<div class="flex items-center justify-center w-12 h-10">
							<div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
						</div>
					{/if}
				</Input>
			</div>

		</div>

		<!-- Word list -->
		<div class="multiple-words-container flex flex-col gap-4">
			{#if multipleWords && multipleWords.length > 0}
				<P2 --text-align=center>Click on any word to change its definition:</P2>
				<div class="border-2 border-gray-800 rounded-lg p-4 bg-blue-100 flex flex-col gap-3 max-h-96 overflow-y-auto">
					{#each multipleWords as wordData, wordIndex}
						<div class="flex justify-between items-center gap-4">
							<div
								class="bg-white border-2 border-gray-800 rounded-lg p-4 flex justify-between items-center cursor-pointer transition-all duration-200 text-left w-full hover:transform hover:-translate-y-0.5 hover:shadow-lg hover:border-blue-500 {wordData.definitions.length === 0 ? 'opacity-60 cursor-not-allowed' : ''}"
								onclick={() => openDefinitionSelection(wordIndex)}
								role="button"
								tabindex="0"
								onkeydown={(e) => e.key === 'Enter' && openDefinitionSelection(wordIndex)}
							>
								<div class="flex-1 flex flex-col gap-1">
									<H3>{wordData.word}</H3>
									{#if wordData.definitions.length === 0}
										<P2 --text-color="var(--rose)">No definitions found</P2>
									{:else}
										<P2 --text-color="var(--charcoal)">{wordData.selectedDefinition || 'No definition selected'}</P2>
									{/if}
								</div>
							</div>
							<button
									class="bg-red-500 text-white border-2 border-gray-800 rounded-full w-8 h-8 flex items-center justify-center cursor-pointer text-xl font-bold transition-all duration-200 flex-shrink-0 hover:bg-red-600 hover:scale-110 leading-none"
									onclick={() => removeWordFromList(wordIndex)}
									title="Remove word"
								>
									×
							</button>
						</div>
					{/each}
				</div>

				<div class="multiple-words-actions mt-4">
					<Button
						--button-bg-color="var(--sky-blue)"
						onclick={addAllCards}
						disabled={isAddingCard || multipleWords.filter(w => w.selectedDefinition).length === 0}
					>
						{isAddingCard ? 'Adding Cards...' : `Add ${multipleWords.filter(w => w.selectedDefinition).length} Cards`}
					</Button>
				</div>
			{:else}
				<div class="border-2 border-dashed border-gray-800 rounded-lg p-8 text-center bg-blue-100">
					<P2 --text-align=center --text-color="var(--charcoal)">No words added yet. Enter a word above.</P2>
				</div>
			{/if}


		</div>
	</div>
	{:else if isDeleteConfirmOpen}
	<div class="flex flex-col gap-4 min-w-80">
		<H2 --text-align=center>Delete Deck</H2>
		<P2 --text-align=center>
			Are you sure you want to delete the deck "{deckToDelete?.name}"? This action cannot be undone.
		</P2>
		<div class="flex gap-4 justify-center items-center mt-4">
			<Button --button-bg-color="var(--rose)" onclick={confirmDelete}>Yes, Delete</Button>
			<Button isSecondary onclick={closePopUp}>Cancel</Button>
		</div>
	</div>
	{:else if isSelectDefinitionsOpen}
	<div class="flex flex-col gap-6 min-w-96 max-w-2xl">
		<H2 --text-align=center>Select Definitions for "{currentWord}"</H2>
		{#if currentWordIndex !== null}
			<P2 --text-align=center --text-color="var(--sky-blue)">Word {currentWordIndex + 1} of {multipleWords?.length || 0}</P2>
		{/if}
		
		{#if wordDefinitions && wordDefinitions.length > 0}
			<!-- Tab buttons (only show if more than one POS group) -->
			{#if wordDefinitions.length > 1}
				<div class="flex gap-1 bg-blue-100 rounded-lg p-1 border-2 border-black">
					{#each wordDefinitions as posGroup}
						<button 
							class="flex-1 px-4 py-3 border-none rounded-md cursor-pointer transition-all duration-200 font-semibold uppercase tracking-wider text-sm
								{activePOSTab === posGroup.POS 
									? 'bg-blue-500 text-white shadow-[2px_2px_0px_black]' 
									: 'bg-transparent text-gray-600 hover:bg-blue-500 hover:text-white'}"
							onclick={() => activePOSTab = posGroup.POS}
						>
							{posGroup.POS}
						</button>
					{/each}
				</div>
			{/if}
			
			<!-- Active tab content -->
			<div class="h-80 bg-white border-2 border-black rounded-lg flex flex-col">
				{#each wordDefinitions as posGroup}
					{#if activePOSTab === posGroup.POS}
						<!-- Fixed header -->
						<div class="border-b-2 border-gray-300 pb-2 p-4 bg-white rounded-t-lg flex-shrink-0">
							<H3 --text-color="var(--sky-blue)">{posGroup.POS}</H3>
						</div>
						<!-- Scrollable definitions -->
						<div class="flex-1 overflow-y-auto p-4 min-h-0">
							<div class="flex flex-col gap-2">
								{#each posGroup.definitions as def}
									<button
										onclick={() => selectDefinition(def)}
										class="w-full p-3 text-left border-2 rounded-lg transition-all duration-200 cursor-pointer
											{isDefinitionSelected(def)
												? 'border-blue-500 bg-blue-50 shadow-[2px_2px_0px_rgb(59,130,246)]'
												: 'border-gray-300 bg-white hover:border-blue-400 hover:bg-blue-50 hover:shadow-[1px_1px_0px_rgb(147,197,253)]'}"
									>
										<P2>{def}</P2>
									</button>
								{/each}
							</div>
						</div>
					{/if}
				{/each}
			</div>
		{/if}
		<div class="flex gap-4 justify-center items-center">
			<Button
				onclick={() => {
					if (currentWordIndex !== null && multipleWords && selectedDefinition) {
						multipleWords[currentWordIndex].selectedDefinition = selectedDefinition;
					}
					popUpState = "Add Cards";
				}}
				disabled={!selectedDefinition}
			>
				{selectedDefinition ? 'Select Definition' : 'Select a Definition'}
			</Button>
			<Button isSecondary onclick={() => popUpState = "Add Cards"}>Back</Button>
		</div>

	</div>
	{:else if isImportQuizletOpen}
	<div class="import-quizlet-popup-content">
		<H2 --text-align=center>Import from Quizlet</H2>

		{#if !quizletVocabularies}
			<!-- Step 1: Get Quizlet URL -->
			<Input
				label="Quizlet URL:"
				id="quizlet-url"
				placeholder="https://quizlet.com/..."
				required
				fullWidth
				bind:value={quizletUrl}
				disabled={isImportingQuizlet}
				onkeydown={(e) => e.key === 'Enter' && !isImportingQuizlet && fetchQuizletData()}
			/>
			<div class="import-quizlet-actions">
				<Button
					--button-bg-color="var(--aquamarine)"
					onclick={fetchQuizletData}
					disabled={isImportingQuizlet}
				>
					{isImportingQuizlet ? 'Importing...' : 'Import'}
				</Button>
				<Button isSecondary onclick={closePopUp} disabled={isImportingQuizlet}>Cancel</Button>
			</div>
		{:else}
			<!-- Step 2: Create Deck -->
			<div class="flex flex-row gap-2 justify-center items-center">
				<P1 --text-align=center --text-color="var(--green)">Found {quizletVocabularies.length} vocabulary items.</P1>
				<P1>Create new deck:</P1>
			</div>
			<Input
				label="Deck name:"
				id="import-deck-name"
				placeholder="Enter deck name"
				required
				fullWidth
				bind:value={importDeckName}
				disabled={isCreatingDeck}
				onkeydown={(e) => e.key === 'Enter' && !isCreatingDeck && createDeckQuizlet()}
			/>
			<Input
				label="Description:"
				id="import-deck-desc"
				placeholder="Enter description"
				fullWidth
				bind:value={importDeckDescription}
				disabled={isCreatingDeck}
				onkeydown={(e) => e.key === 'Enter' && !isCreatingDeck && createDeckQuizlet()}
			/>
			<div class="import-quizlet-actions">
				<Button
					--button-bg-color="var(--sky-blue)"
					onclick={createDeckQuizlet}
					disabled={isCreatingDeck}
				>
					{isCreatingDeck ? 'Creating...' : 'Create Deck'}
				</Button>
				<Button isSecondary onclick={() => { quizletVocabularies = null; }} disabled={isCreatingDeck}>Back</Button>
				<Button isSecondary onclick={closePopUp} disabled={isCreatingDeck}>Cancel</Button>
			</div>
		{/if}

	</div>
	{/if}

	<!-- Unified Message Display -->
	{#if popUpMessage}
		<div class="mt-4">
			<P2 --text-align=center --text-color={messageState === 'error' ? 'var(--rose)' : messageState === 'success' ? 'var(--green)' : 'var(--charcoal)'}>{@html popUpMessage}</P2>
		</div>
	{/if}
</PopUp>

<!-- Success Notification -->
{#if showNotification}
	<div class="notification-popup">
		<div class="flex items-center gap-2">
			<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="white"/>
			</svg>
			<P2 --text-color="white">{notificationMessage}</P2>
		</div>
	</div>
{/if}

<style>
	.decks-bg {
		min-height: 100vh;
		background: var(--sky-blue);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
        gap: 2rem;
	}

	.deck-header {
		display: inline-flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		gap: 0.5rem;
        border-bottom: 1px solid var(--pitch-black);
		padding-bottom: 0.5rem;
	}

	.decks-header {
		display: flex;
		justify-content: center;
        padding: 0.75rem 3rem;
        background: var(--aquamarine);
        border: 0.5rem solid var(--pitch-black);
        justify-self: start;
	}

	.decks-main {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.decks-card {
		background: var(--white);
		border-radius: 1rem;
		border: 0.125rem solid var(--pitch-black);
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		padding: 2rem;
		max-width: 50rem;
		width: calc(100% - 8rem);
	}

	.decks-list-header, .decks-list-row {
		display: flex;
		align-items: center;
		width: 100%;
	}
	.decks-list-header {
		font-weight: 700;
		border-bottom: 0.125rem solid var(--pitch-black);
		background: var(--white);
		padding: 0.75rem 0;
	}

	.decks-list-row {
		transition: background 0.2s;
		padding: 0.75rem 1rem;
	}
	.decks-list-row:hover {
		background: var(--light-sky-blue);
		text-decoration: underline;
		cursor: pointer;
	}

	.deck-name-col {
		flex: 1 1 0;
		text-align: left;
		padding-right: 1rem;
		min-width: 0;
		word-break: break-word;
	}

	.stat-col {
		flex: 1 0 0;
		text-align: center;
		--text-align: center;
		position: relative;
	}

    .remove-col {
        width: 3rem;
        text-align: center;
        position: relative;
    }

	.decks-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		margin-top: 1rem;
        align-items: center;
	}

    .delete-btn {
        visibility: hidden;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }

    .decks-list-row:hover .delete-btn {
        visibility: visible;
    }

	.add-deck-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		align-items: center;
		margin-top: 1rem;
	}

	@media (max-width: 768px) {
		.decks-card {
			padding: 1.25rem 0.25rem 1.25rem 0.25rem;
            width: calc(100% - 2rem);
		}

		.decks-list-header, .decks-list-row {
			padding: 0.5rem 0.5rem;
		}

        .delete-btn {
            width: 1rem;
            visibility: visible;
            margin: 0;
        }

        .remove-col {
            width: 1rem;
        }
	}

	.add-deck-popup-content,
	.edit-deck-popup-content,
	.import-quizlet-popup-content {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		min-width: 400px;
		width: 100%;
		max-width: 600px;
	}

	.import-quizlet-actions,
	.multiple-words-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		align-items: center;
		margin-top: 1rem;
	}

	.multiple-words-container {
		width: 100%;
		max-width: 800px;
	}



	.add-card-vocab-name {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		padding: 2rem;
	}

	.loading-spinner {
		width: 2rem;
		height: 2rem;
		border: 0.25rem solid var(--light-sky-blue);
		border-top: 0.25rem solid var(--sky-blue);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 2rem;
		text-align: center;
		color: var(--charcoal);
	}

	.deck-stats-description {
		margin: 0.5rem 0 1.5rem 0;
		text-align: center;
	}

	.deck-stats-list {
		width: 100%;
		margin-bottom: 2rem;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.deck-stat-row {
		width: 100%;
		max-width: 18rem;
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.5rem;
	}

	.deck-stats-actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
	}

	.cards-list {
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
	}
	
	@media (max-width: 768px) {
		.edit-cards-popup-content {
			min-width: 280px;
			max-width: calc(100vw - 2rem);
		}

		.cards-list {
			max-height: 300px;
		}

		.card-item {
			padding: 0.75rem;
		}
	}

	.notification-popup {
		position: fixed;
		top: 1rem;
		right: 1rem;
		z-index: 50;
		background-color: #22c55e;
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
		border: 2px solid black;
		animation: slideInFade 0.4s ease-out forwards, slideOutFade 0.4s ease-in 2.6s forwards;
	}

	@keyframes slideInFade {
		0% {
			opacity: 0;
			transform: translateX(100%);
		}
		100% {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes slideOutFade {
		0% {
			opacity: 1;
			transform: translateX(0);
		}
		100% {
			opacity: 0;
			transform: translateX(100%);
		}
	}

</style>