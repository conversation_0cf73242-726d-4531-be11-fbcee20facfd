<script lang="ts">
    // Component imports for game UI elements
    import WordCard from "$lib/vocabTool/WordCard.svelte";
    import AnswerButtons from "$lib/vocabTool/AnswerButtons.svelte";
    import { compareCards, convertCardToFSRS, fsrsScheduler, convertFSRSToCard } from '$lib/firebase/vocabDB.js';
    import { Rating, type RecordLogItem } from 'ts-fsrs';
    import { H2, P2 } from "$lib/ui/index.js";
    import type { Card } from "$lib/types";
    import Button from "$lib/ui/Button.svelte";
    import { doc, serverTimestamp, updateDoc } from "firebase/firestore";
    import { db } from "$lib/firebase/firestore.js";
    import posthog from "posthog-js";
    import { onMount } from "svelte";
    import { debounce } from '$lib/utilities.js';

    // Constants
    let { data } = $props();

    // Deck and cards
    let deck = $state(data.deck);
    let cards: Card[] = $state(deck.cards);

    // Sort the cards by due date
    let sortedCards = $derived(cards.filter((card) => {
        const now = Date.now();
        const fifteenMinutesFromNow = new Date(now + 15 * 60 * 1000);
        return card.fsrsCard.due <= fifteenMinutesFromNow;
    }).toSorted(compareCards));
    let hasAllCardsBeenReviewed = $derived(sortedCards.length === 0);

    $effect(() => {
        if (hasAllCardsBeenReviewed && data.hasDueCards) debouncedEndSession();
    });

    let currentCard = $derived(sortedCards?.[0]);

    // Question progression state
    /**
     * 0: Initial state
     * 1: First hint shown
     * 2: Second hint shown
     * 3: The user did not know the word
     */
    let currentPhase: 0 | 1 | 2 | 3 = $state(0);
    let hasUserAnswered = $state(false);
    
    // Performance tracking
    let firstTryCorrectCount = $state(0);
    let correctWithHintCount = $state(0);
    let incorrectCount = $state(0);
    let sessionStartTime = $state(new Date());
    let totalCardsReviewed = $state(0);

    function convertPhaseToRating(phase: number): Rating {
        switch (phase) {
            case 0:
                firstTryCorrectCount++;
                return Rating.Easy;
            case 1:
                correctWithHintCount++;
                return Rating.Good;
            case 2:
                correctWithHintCount++;
                return Rating.Hard;
            case 3:
                incorrectCount++;
                return Rating.Again;
        }
    }

    /**
     * Handles the completion of a single question.
     * Updates performance metrics based on which phase the user succeeded.
     * Automatically determines FSRS rating based on completion phase.
     * Sets focus to the end popup for accessibility.
     */
    async function completeQuestion(wasAnswerCorrect: boolean) {
        // Update metrics based on whether the user answered correctly.
        let rating: Rating;
        if (wasAnswerCorrect) {
            rating = convertPhaseToRating(currentPhase);
        } else {
            rating = Rating.Again;
        }

        // Update the card in the local deck
        const fsrsCard = convertCardToFSRS(currentCard);
        const logItem: RecordLogItem = fsrsScheduler.repeat(fsrsCard, Date.now())[rating];

        // Update the card in the local deck
        cards = cards.map((card) => card.id === currentCard.id ? convertFSRSToCard(logItem.card, currentCard.id, currentCard.vocabCard) : card);

        // Update session metrics
        totalCardsReviewed++;
    }

    /**
     * Advances to the next phase of the current question
     * Each phase provides more hints until the question is eventually completed
     */
    function advancePhase() {
        currentPhase += 1;  
    }

    /**
     * Moves to the next question in the deck
     * Resets question-specific state and checks for level completion
     */
    function moveToNextQuestion() {
        // Reset question state
        hasUserAnswered = false;
        currentPhase = 0;
    }

    function handleSubmitAnswer(wasAnswerCorrect: boolean) {
        completeQuestion(wasAnswerCorrect);
        moveToNextQuestion();      
    }

    function handleKnowButtonClick() {
        hasUserAnswered = true;
    }
    
    // Session state
    let currentScene: 'game' | 'saving' | 'end' = $state('game');
    // @ts-expect-error
    let isSaving = $derived(currentScene === 'saving');
    // @ts-expect-error
    let hasSessionEnded = $derived(currentScene === 'end');

    async function syncDeckToDatabase() {
        const deckRef = doc(db, 'users', data.uid, 'decks', deck.id);
        await updateDoc(deckRef, {
            cards,
            updatedAt: serverTimestamp(),
        });
    } 
    
    async function endSession() {
        currentScene = 'saving';

        posthog.capture('vocab16_session_ended', {
            deck_name: deck.name,
            cards_reviewed: totalCardsReviewed,
            first_try_correct: firstTryCorrectCount,
            needed_hints: correctWithHintCount,
            incorrect: incorrectCount,
            accuracy: totalCardsReviewed > 0 ? Math.round(((firstTryCorrectCount + correctWithHintCount) / totalCardsReviewed) * 100) : 0,
        });

        await syncDeckToDatabase();

        currentScene = 'end';
    }

    const debouncedEndSession = debounce(endSession, 900);
    const debouncedSyncDeckToDatabase = debounce(syncDeckToDatabase, 2000);

    // Handle beforeunload event to prevent accidental window closing
    onMount(() => {
        const handleVisibilityChange = async (event: Event) => {
            if (currentScene !== 'end' && data.hasDueCards && document.visibilityState === 'hidden') {
                debouncedSyncDeckToDatabase();
            }
        };

        // Add event listener when component mounts
        document.onvisibilitychange = handleVisibilityChange;

        // Clean up event listener when component unmounts
        return () => {
            document.onvisibilitychange = null;
        };
    });
</script>

<svelte:head>
    <title>Vocab16 - {deck.name}</title>
</svelte:head>

<!-- Main game container -->
<section class="flashcard-container" class:center={isSaving || !data.hasDueCards || hasSessionEnded || !currentCard}>  
    {#if !data.hasDueCards}
        <H2>There are no cards due for review!</H2>
        <P2>Please come back later to review more cards.</P2>
        <a href="/study/vocab-tool"><Button --button-bg-color="var(--aquamarine)" >Back to Deck</Button></a>
    {:else}
        {#if hasSessionEnded}
        {@const now = new Date().getTime()}
        {@const duration = now - sessionStartTime.getTime()}
        {@const hours = Math.floor(duration / 3600000)}
        {@const minutes = Math.floor(duration / 60000)}
        {@const seconds = Math.floor((duration % 60000) / 1000)}
        <!-- Session completion summary -->
        <div class="session-complete">
            <div class="completion-card">
            <H2>Session Complete! 🎉</H2>
            
            <div class="session-stats">
                <div class="stat-item">
                <P2 isBold>Cards Reviewed:</P2>
                <P2 isBold>{totalCardsReviewed}</P2>
                </div>
                
                <div class="stat-item">
                <P2 isBold>Duration:</P2>
                <P2 isBold>
                    {hours > 0 ? `${hours.toString().padStart(2, '0')}:` : ''}{minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}s
                </P2>
                </div>
                
                <div class="stat-item">
                <P2 --text-color="var(--aquamarine)" isBold>First Try Correct:</P2>
                <P2 --text-color="var(--aquamarine)" isBold>{firstTryCorrectCount}</P2>
                </div>
                
                <div class="stat-item">
                <P2 --text-color="var(--tangerine)" isBold>Needed Hints:</P2>
                <P2 --text-color="var(--tangerine)" isBold>{correctWithHintCount}</P2>
                </div>
                
                <div class="stat-item">
                <P2 --text-color="var(--rose)" isBold>Incorrect:</P2>
                <P2 --text-color="var(--rose)" isBold>{incorrectCount}</P2>
                </div>
                
                <div class="stat-item accuracy">
                <P2 isBold --text-color="var(--purple)">Accuracy:</P2>
                <P2 isBold --text-color="var(--purple)">{totalCardsReviewed > 0 ? Math.round(((firstTryCorrectCount + correctWithHintCount) / totalCardsReviewed) * 100) : 0}%</P2>
                </div>
            </div>
            <a href="/study/vocab-tool"><Button --button-bg-color="var(--aquamarine)" >Back to Deck</Button></a>
            </div>
        </div>
        {:else if isSaving}
            <H2>Saving...</H2>
        {:else if currentCard}
        <!-- End session button -->
        <div class="end-session-button">
            <Button --button-bg-color="var(--tangerine)" onclick={debouncedEndSession}>End Session</Button>
        </div>

        <!-- Game content container -->
        <div class="content-container">
            <!-- Word card showing the vocabulary word and hints -->
            <WordCard {currentCard} {currentPhase} {hasUserAnswered} />
            
            <AnswerButtons
                {handleKnowButtonClick}
                handleDontKnowButtonClick={advancePhase}
                {handleSubmitAnswer}
                {hasUserAnswered}
                {currentPhase}
            /> 
        </div>
        {:else}
            <H2>Loading...</H2>
        {/if}
    {/if}
</section>

<style>
    /* Main container with sky blue background */
    .flashcard-container {
        background-color: var(--sky-blue);
        min-height: 100vh;
        width: 100vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        text-align: center;
        padding: 1rem 4rem;
        gap: 2rem;
    }

    .flashcard-container.center {
        justify-content: center;
    }

    .end-session-button {
        display: flex;
        justify-content: flex-end;
        width: 100%;
    }

    /* Container for game content (word card and inputs) */
    .content-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-self: center;
        width: 100%;
        max-width: 64rem;
    }

    /* Session completion styles */
    .session-complete {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        max-width: 37.5rem;
    }

    .completion-card {
        background-color: white;
        border: 3px solid black;
        border-radius: 1rem;
        box-shadow: 1.25rem 1.25rem var(--pitch-black);
        padding: 3rem 2rem;
        text-align: center;
        width: 100%;
        animation: popupAppear 0.25s ease-out;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .session-stats {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 0.5rem;
        font-family: 'Inter', sans-serif;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
        .flashcard-container {
        padding: 1rem;
        gap: 2rem;
        }

        .content-container {
        margin-bottom: 0.625rem;
        }

        .completion-card {
        padding: 2rem 1.5rem;
        }
    }
</style>