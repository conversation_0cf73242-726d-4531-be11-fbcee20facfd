import { json } from "@sveltejs/kit"
import { supabase } from '$lib/server';
import type { <PERSON>quest<PERSON>andler } from './$types';
import { adminDB as db } from "$lib/server";
import { convertFSRSToCard } from "$lib/firebase/vocabDB";


export const GET: RequestHandler = async (event) => {
    await migrateSupabase(event);
    await migrateFirebase();
    return json({ success: true });
}

async function migrateSupabase(event) {
    const { data: queryData, error } = await supabase
        .from('vocab')
        .select('vocab');

    const wordListBefore = queryData.map(d => d.vocab)
    const wordList = (await Promise.all(wordListBefore.map(async vocab => {
        const { data, error } = await supabase
            .from('vocabCards')
            .select('word')
            .eq('word', vocab)
            .maybeSingle();
        return data ? null : vocab;
    }))).filter(Boolean);

    const wordListWithDefinition: { word: string, definition: string }[] = [];
    const failedWords = [];
    for (const [index, word] of wordList.entries()) {
        console.log(`Fetching definition for ${word} (${index + 1} of ${wordList.length})`);

        const dictionaryData = await getDictionaryData(word);

        if (dictionaryData.length === 0) {
            console.log(`No definitions found for ${word}`);
            failedWords.push(word);
            continue;
        }

        const definition = dictionaryData[0].definitions[0];
        wordListWithDefinition.push({ word, definition });

        if (index % 30 === 0) {
            console.log("Sleeping for 1 minute to avoid rate limit...");
            await sleep(60000);
        }
    }

    console.log(`Failed to find definitions for ${failedWords.length} words: ${failedWords.join(', ')}`);

    await event.fetch('/api/vocab-tool/generate-vocab-data', {
        method: 'POST',
        body: JSON.stringify({
            uid: '1',
            wordList: wordListWithDefinition
        })
    });

    console.log("");
    console.log(`Generate and write ${wordListWithDefinition.length} vocab cards to database`);

    return { wordListWithDefinition, failedWords };
}


async function getDictionaryData(word) {
    const dictionaryApiResponse = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${word}`)
    const dictionaryData = await dictionaryApiResponse.json();

    if (dictionaryData.title === "No Definitions Found") {
        return [];
    }

    const extractedData: {POS: string, definitions: string[]}[] = Object.values(
    dictionaryData
        .map(wordEntry => wordEntry.meanings)
        .flat()
        .reduce((acc, posGroup) => {
            const pos = posGroup.partOfSpeech;
            if (!acc[pos]) {
                acc[pos] = { POS: pos, definitions: [] };
            }
            acc[pos].definitions.push(
                ...posGroup.definitions.map(def => def.definition.trim())
            );
            return acc;
        }, {})
    );
    
    return extractedData;
}


function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}


async function migrateFirebase() {
    const usersSnap = await db.collection('users').get();
    await Promise.all(usersSnap.docs.map(doc => migrateSingleUser(doc.id)));
    // await migrateSingleUser('ZUIN6faaaZWVDCLufiG80uJZPwH3');  // id Phú
}


async function migrateSingleUser(userId: string) {
    const deckSnap = await db
        .collection('users')
        .doc(userId) 
        .collection('decks')
        .get();

    if (deckSnap.empty) return;

    console.log(`User ${userId} has ${deckSnap.size} decks`);
    
    deckSnap.forEach(async deckDoc => {
        const deckId = deckDoc.id;
        const cards = deckDoc.data().cards;

        const updatedCards = await Promise.all(
            cards.map(async card => {
                if (card.vocabCard) return card;

                const { id, vocabId, ...FSRSCard } = card;

                const { data: oldData } = await supabase
                    .from('vocab')
                    .select('vocab')
                    .eq('vocabId', vocabId)
                    .maybeSingle();

                if (!oldData) return null;

                const { data: vocabCard } = await supabase
                    .from('vocabCards')
                    .select('word, partOfSpeech, definition, charge, difficulty, hint1, hint2')
                    .eq('word', oldData.vocab)
                    .single();

                if (!vocabCard) return null;

                return convertFSRSToCard(FSRSCard, id, vocabCard);
            })
        );

        // Filter out nulls after awaiting
        const filteredCards = updatedCards.filter(Boolean);

        await db
            .collection('users')
            .doc(userId)
            .collection('decks')
            .doc(deckId)
            .update({ cards: filteredCards });
    });
}